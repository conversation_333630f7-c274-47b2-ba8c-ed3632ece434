package com.datatricks.kafkacommondomain.model.inject;

import com.datatricks.kafkacommondomain.model.PageableDto;
import com.datatricks.kafkacommondomain.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ContractInjectDto implements PageableDto {

    @Schema(description = "id of the contract.", example = "1")
    private Long id;

    @Schema(description = "Reference of the contract.", example = "SOC_9805862")
    @NotBlank(message = "reference:please provide a reference")
    private String reference;

    @NotBlank(message = "title:please provide a title")
    @Schema(description = "Title of the contract.", example = "title")
    private String title;

    @JsonProperty("refinancing_rate")
    @Schema(description = "Refinancing rate of the contract.", example = "1.5")
    private Double refinancingRate;

    @JsonProperty("business_type")
    @NotBlank(message = "business_type:please provide a business type")
    @Schema(description = "Business type of the contract.", example = "A/R or NCAC or NCNC")
    private String businessType;

    @JsonProperty("market_type")
    @NotBlank(message = "market_type:please provide a market type")
    @Schema(description = "Market type of the contract.", example = "[Autres, Divers, Healthcare, IT, Industrie, Mobile, Motors, NA]")
    private String marketType;

    @JsonProperty("external_reference")
    @Schema(description = "External reference of the contract.", example = "SOC_9805863")
    private String externalReference;

    @JsonProperty("lessor_reference")
    @Schema(description = "Lessor reference of the contract.", example = "SOC_9805865")
    private String lessorReference;

    @JsonProperty("agreement_as_service")
    @Schema(description = "Agreement as service of the contract.", example = "true")
    private Boolean agreementAsService;

    @JsonProperty("agreement_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Agreement date of the contract.", example = "2021-01-01")
    private Date agreementDate;

    @JsonProperty("end_agreement_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End agreement date of the contract.", example = "2022-01-01")
    private Date endAgreementDate;

    @JsonProperty("sign_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Sign date of the contract.", example = "2021-01-01")
    private Date signDate;

    @JsonProperty("rental_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Rental date of the contract.", example = "2021-01-01")
    private Date rentalDate;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date of the contract.", example = "2026-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date of the contract.", example = "2027-01-01")
    private LocalDate endDate;

    @JsonProperty("deadline")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Deadline of the contract.", example = "2027-01-02")
    private LocalDate deadline;

    @JsonProperty("lessor_selling_price")
    @Schema(description = "Lessor selling price of the contract.", example = "1.5")
    private Double lessorSellingPrice;

    @JsonProperty("amount_rental_base")
    @Schema(description = "Amount rental base of the contract.", example = "1.5")
    private Double amountRentalBase;

    @JsonProperty(value = "amount_granted")
    @Schema(description = "Amount granted of the contract.", example = "1.5")
    private Double amountGranted;

    @JsonProperty("duration")
    @Schema(description = "Duration per days of the contract.", example = "365")
    private Integer duration;

    @JsonProperty("request_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Request date of the contract.", example = "2021-01-01")
    private Date requestDate;

    @JsonProperty("lessor_payment_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Lessor payment date of the contract.", example = "2021-01-01")
    private Date lessorPaymentDate;

    @JsonProperty("business_introducer")
    @Schema(description = "Business introducer of the contract", example = "Linus Torvalds")
    private String businessIntroducer;

    @JsonProperty("management_company")
    @Schema(description = "Management company of the contract")
    @Valid
    @NotNull(message = "management_company:please provide a management company")
    private ActorInjectDto managementCompany;

    @Valid
    @NotNull(message = "activity:please provide an activity")
    private ActivityInjectDto activity;

    @Valid
    @NotNull(message = "product:please provide an activity")
    private ProductInjectDto product;

    @Valid
    @NotNull(message = "currency:please provide an activity")
    private CurrencyInjectDto currency;

    @Valid
    @NotNull(message = "phase:please provide an activity")
    private PhaseInjectDto phase;

    @Valid
    @NotNull(message = "milestone:please provide an activity")
    private MilestoneInjectDto milestone;

    @JsonProperty("contract_actor")
    @Schema(description = "Contract Actor of the contract")
    @NotNull(message = "contract_actor:please provide a contract actor")
    private List<@Valid ContractActorInjectDto> contractActor;

    @NotNull(message = "assets:please provide a list of assets")
    @Valid
    private AssetInjectDto asset;

    public ContractInjectDto(Long id) {
        this.id = id;
    }
}
