package com.datatricks.kafkacommondomain.model.inject;

import com.datatricks.kafkacommondomain.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ElementInjectDto {
    
    private Long id;

    @JsonProperty("serial_number")
    @NotBlank(message = "element.serial_number:please provide a serial number")
    private String serialNumber;

    @JsonProperty("external_reference")
    private String externalReference;

    private String label;

    @JsonProperty("order_number")
    private long orderNumber;

    private String brand;

    private String model;

    private String description;

    private String license;

    private String vin;

    @JsonProperty("customer_address_assignment_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date customerAddressAssignmentDate;

    @JsonProperty("supplier_address_assignment_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date supplierAddressAssignmentDate;

    @JsonProperty("short_description")
    private String shortDescription;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @JsonProperty("acquisition_value_HT")
    @Schema(description = "Acquisition value HT", example = "117")
    private double acquisitionValueHt;

    @JsonProperty("acquisition_value")
    @Schema(description = "Acquisition value", example = "119.925")
    private double acquisitionValue;

    @Schema(description = "Condition of the element", example = "NEW")
    private ElementCondition condition;

    private String registration;

    @JsonProperty("amortization")
    @NotNull(message = "amortization:please provide an amortization")
    @Valid
    private AmortizationInjectDto amortization;

    @JsonProperty("client_address")
    @NotNull(message = "client_address:please provide a client address")
    @Valid
    private AddressInjectDto clientAddress;

    @JsonProperty("provider_address")
    @NotNull(message = "provider_address:please provide a provider address")
    @Schema(description = "Provider address", example = "provider_address")
    @Valid
    private AddressInjectDto providerAddress;

    @JsonProperty("tax")
    @NotNull(message = "tax:please provide a tax")
    @Valid
    private TaxRateInjectDto tax;

    @NotNull(message = "phase:please provide a phase")
    @Valid
    private PhaseInjectDto phase;

    @NotNull(message = "milestone:please provide a milestone")
    @Valid
    private MilestoneInjectDto milestone;

    @NotNull(message = "currency:please provide a currency")
    @Valid
    private CurrencyInjectDto currency;

    @NotNull(message = "category:please provide an category")
    @Valid
    private CategoryInjectDto category;

    @Valid
    private ShippingInjectDto shipping;

    @JsonProperty("supplier")
    @NotNull(message = "supplier:please provide a supplier")
    @Valid
    private ActorInjectDto supplier;
}
