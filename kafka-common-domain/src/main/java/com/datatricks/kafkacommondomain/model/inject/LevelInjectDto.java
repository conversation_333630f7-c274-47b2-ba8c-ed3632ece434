package com.datatricks.kafkacommondomain.model.inject;

import com.datatricks.kafkacommondomain.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class LevelInjectDto {
    @JsonProperty("id")
    @Schema(description = "id of the level", example = "1")
    private Long id;

    private Integer order;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "levels.start_date:start date cannot be null")
    @Schema(description = "start date of the level", example = "2021-01-01")
    private Date startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "levels.end_date:end date cannot be null")
    @Schema(description = "end date of the level", example = "2022-01-01")
    private Date endDate;

    @JsonProperty("due_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "due date of the level", example = "2022-01-01")
    private Date dueDate;

    @JsonProperty("payment_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "payment date of the level", example = "2022-01-01")
    private Date paymentDate;

    @JsonProperty("period")
    @NotNull(message = "levels.period:period cannot be null")
    @Schema(description = "period of the level", example = "ENUM_ANNEE")
    private TypeUnitePeriode period;

    @JsonProperty("period_multiple")
    @NotNull(message = "levels.period_multiple:period multiple cannot be null")
    @Schema(description = "period multiple of the level", example = "1")
    private Integer periodMultiple;

    @JsonProperty("period_number")
    @NotNull(message = "levels.period_number:period numbers cannot be null")
    @Schema(description = "period number of the level", example = "3")
    private Integer periodNumber;

    @JsonProperty("rent")
    @NotNull(message = "levels.rent:rent cannot be null")
    @Schema(description = "rent of the level", example = "10000.0")
    private Double rent;

    @JsonProperty("perception")
    @NotNull(message = "levels.perception:perception mode cannot be null")
    @Schema(description = "perception mode of the level", example = "ENUM_AVANCE")
    private TypeTerme perception;

    @JsonProperty("rate")
    @Schema(description = "rate of the level", example = " 1.9999561482790003")
    private Double rate;

    @JsonProperty("nominal_rate")
    @Schema(description = "nominal rate of the level", example = " 1.9999561482790003")
    private Double nominalRate;
}