package com.datatricks.kafkacommondomain.model.inject;

import com.datatricks.kafkacommondomain.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AccessoryInjectDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("title")
    @NotBlank(message = "title:please provide a title for this accessory")
    @Schema(description = "title of the rental", example = "Acc 1")
    private String title;

    @JsonProperty("line_type")
    @Schema(description = "line_type code of the accessory")
    private LineTypeInjectDto lineType;

    @JsonProperty("arrangement_type")
    @NotBlank(message = "arrangement_type:please provide an arrangement type for this accessory")
    @Schema(description = "arrangement type of the accessory", example = "FINANCIAL or OPERATIONAL")
    private String arrangementType;

    @NotNull(message = "amount:please provide an amount for this accessory")
    @JsonProperty("amount")
    @Schema(description = "amount of the rental", example = "1000.0")
    private Double originalAmount;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "startDate:please provide a start date for this accessory")
    @Schema(description = "start date of the rental", example = "2021-01-01")
    private Date startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "endDate:please provide an end date for this accessory")
    @Schema(description = "end date of the rental", example = "2022-01-01")
    private Date endDate;

    @JsonProperty("separate_invoice")
    @Schema(description = "separate invoice of the accessory", example = "true")
    private Boolean separateInvoice;

    @JsonProperty("suspended_invoice")
    @Schema(description = "suspended invoice of the accessory", example = "true")
    private Boolean suspendedInvoice;

    @JsonProperty("mobile_extension")
    @Schema(description = "mobile extension of the accessory", example = "true")
    private Boolean mobileExtension;

    @JsonProperty("calculation_basis")
    @NotNull(message = "calculation_basis:please provide a calculation basis for this accessory")
    @Schema(description = "calculation basis of the accessory", example = "ENUM_BASE360")
    private TypeBase calculationBasis;

    @JsonProperty("calculation_mode")
    @NotNull(message = "calculation_mode:please provide a calculation mode for this accessory")
    @Schema(description = "calculation mode of the accessory", example = "ENUM_BASE360")
    private TypeBase calculationMode;

    @JsonProperty("tax")
    @Schema(description = "tax of the accessory", example = "TVA")
    private String tax;

    @JsonProperty("tax_rate")
    @Schema(description = "tax rate of the accessory", example = "10.0")
    private Double taxRate;

    @JsonProperty("status")
    @Schema(description = "status of the accessory", example = "INITIALIZED")
    private FinancingStatus status;

    @JsonProperty("levels")
    @Schema(description = "levels of the accessory")
    private List<@Valid LevelInjectDto> accessoryLevelsList;
}
