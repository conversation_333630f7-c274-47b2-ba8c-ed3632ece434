version: "3"

# Build Arguments
x-build-args: &build-args
  dockerfile: Dockerfile
  args:
    CI_PROJECT_ID: ${CI_PROJECT_ID}
    CI_API_V4_URL: ${CI_API_V4_URL}
    CI_JOB_TOKEN: ${CI_JOB_TOKEN}
    TOKEN_TYPE: Deploy-Token

x-common-env: &common-env
  SPRING_PROFILES_ACTIVE: docker-local
  REDIS_CLUSTER_HOST: *********
  REDIS_CLUSTER_PORT: 6379

services:
  #-------------------------------------

    
  #-------------------------------------

  # support-tickets:
  #   container_name: support-tickets
  #   build:
  #     context: ./Support-tickets-ms
  #     dockerfile: Dockerfile
  #   ports:
  #     - "8862:8082"
  #   env_file:
  #     - ./ENV/Support-tickets-ms/.env.docker-local
  #   command:
  #     [
  #       "sh",
  #       "-c",
  #       "npx sequelize-cli db:migrate  --config ./config/app-config.js --env default && npm run start-local",
  #     ]
  #   networks:
  #     - kyra-network
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 512M
  #       reservations:
  #         memory: 256M
    

  #-------------------------------------
  # redis:
  #   image: redis:latest
  #   container_name: redis_container
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   command: redis-server --appendonly yes
  #   networks:
  #     - kyra-network
  #   restart: unless-stopped

  #-------------------------------------


  # #-------------------------------------
  # mongo:
  #   image: mongo
  #   restart: always
  #   environment:
  #     MONGO_INITDB_ROOT_USERNAME: root
  #     MONGO_INITDB_ROOT_PASSWORD: example
  #     MONGO_INITDB_DATABASE: dt-notifications
  #   ports:
  #     - "27017:27017"
  #   volumes:
  #     - mongo-data:/data/db
  #   networks:
  #     - kyra-network
  # #-------------------------------------
  # mongo-express:
  #   image: mongo-express
  #   restart: always
  #   ports:
  #     - 8081:8081
  #   environment:
  #     ME_CONFIG_MONGODB_ADMINUSERNAME: root
  #     ME_CONFIG_MONGODB_ADMINPASSWORD: example
  #     ME_CONFIG_MONGODB_URL: **********************************/
  #     ME_CONFIG_BASICAUTH: false
  #   networks:
  #     - kyra-network
  #   #-------------------------------------
  # notifications:
  #   container_name: notifications
  #   restart: always
  #   build:
  #     context: ./Notifications-ms
  #     dockerfile: Dockerfile
  #   env_file:
  #     - ./ENV/Notifications-ms/.env.docker-local
  #   networks:
  #     - kyra-network
  #   depends_on:
  #     - mongo
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 512M
  #       reservations:
  #         memory: 256M
  #--------------------------------------------------------------------------
  # pgbouncer:
  #   image: edoburu/pgbouncer
  #   volumes:
  #     - ./pgbouncer/pgbouncer.ini:/etc/pgbouncer/pgbouncer.ini:ro
  #     - ./pgbouncer/userlist.txt:/etc/pgbouncer/userlist.txt
  #   networks:
  #     - kyra-network
  #   logging:
  #     driver: json-file
  #     options:
  #       max-size: "10m"
  #       max-file: "3"
  #   depends_on:
  #     - postgres
  # #--------------------------------------------------------------------------
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SERVER_ID: 1
    ports:
      - "2181:2181"
    networks:
      - kyra-network  
  broker:
    image: confluentinc/cp-kafka:latest
    container_name: broker
    ports:
      - "9092:9092"
    environment:
      KAFKA_ADVERTISED_LISTENERS: INTERNAL://broker:19092,EXTERNAL://*************:9092 # put local ip here -> ipconfig in cmd
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT,DOCKER:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: INTERNAL
      KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181"
      KAFKA_BROKER_ID: 1
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1 # Ensure this matches your broker count
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1   # Also matches your broker count
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1 # Set according to your requirements
    networks:
      - kyra-network  
    depends_on:
      - zookeeper

  # kyra-frontend:
  #   container_name: kyra-frontend
  #   image: kyra-front:latest
  #   ports:
  #     - "8639:8639"
  #   networks:
  #     - kyra-network    

networks:
  kyra-network:
    driver: bridge
volumes:
  postgres:
  pgadmin:
  # mongo-data:
  # redis_data: