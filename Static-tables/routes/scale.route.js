const express = require("express");
const router = express.Router();
const ScalesController = require("../controllers/scales.controller");
const  ScalesValidation = require("../validation-rules/scale.rule");
const validateMiddleware = require("../middlewares/validate.middleware");

require("express-async-errors");

router.post("/", validateMiddleware(ScalesValidation.create), ScalesController.createScale);
router.get("/:code", ScalesController.getOne);
router.get("/", ScalesController.search);

router.patch("/:code", ScalesController.updateStatus);
router.put("/:code", ScalesController.updateScale)
router.delete("/:code/equipments/:equipmentCode", ScalesController.removeScaleEquipment);
router.delete("/:code", ScalesController.remove);
router.delete("/:code/third-parties/:actor_reference", ScalesController.removeThirdParty);
router.delete("/:code/services/:serviceReference", ScalesController.removeScaleService);

module.exports = router;