const {
  ServicesPackModel,
  ServiceServicesPackModel,
  ServiceModel,
  OperationModel,
  ProductsModel,
  NapModel,
  PartnerModel,
  CurrencyModel,
  TaxModel,
  ServiceActorModel,
  sequelize,
} = require("../db");

const { Sequelize, Op } = require("sequelize");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const { v4: uuidv4 } = require("uuid");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");

async function createServicesPack(req, res, next) {
  const payload = req.body;
  console.debug("Received payload for services pack:", payload);

  const transaction = await sequelize.transaction();
  console.debug("Transaction started for services pack creation");

  try {
    const newServicesPack = uuidv4();
    // Create the services pack
    const servicesPackData = {
      code: payload.code ||`REG_${newServicesPack.split('-')[0]}`,
      reference: payload.reference || newServicesPack,
      label: payload.label,
      invoice_grouping_code: payload.invoice_grouping_code,
      grouping_code_regulation: payload.grouping_code_regulation,
      type: payload.type || "COMPATIBLE",
    };

    const servicesPack = await ServicesPackModel.create(servicesPackData, {
      transaction,
    });
    const servicesPackCode = servicesPack.code;

    // Associate services with the services pack
    if (payload.services && payload.services.length > 0) {
      // Remove duplicates from the services array
      const uniqueServices = [
        ...new Set(payload.services.map((s) => s.trim())),
      ];

      await Promise.all(
        uniqueServices.map(async (serviceReference) => {
          try {
            // Check if service exists
            const service = await ServiceModel.findOne({
              where: { reference: serviceReference },
              transaction,
            });

            if (!service) {
              console.warn(
                `Service with reference ${serviceReference} not found.`
              );
              return;
            }

            // Create association
            await ServiceServicesPackModel.create(
              {
                reference: uuidv4(),
                service_reference: serviceReference,
                service_pack_code: servicesPackCode,
              },
              { transaction }
            );
          } catch (error) {
            console.error(
              `Error associating service ${serviceReference}:`,
              error.message
            );
          }
        })
      );
    }

    // Send sync message for event streaming
    await sendSyncMessage({
      entityName: "ServicesPack",
      entityReference: servicesPackCode,
      operationType: OperationType.POST,
    });

    await transaction.commit();
    res.status(201).send({
      data: servicesPack,
      message: "Services pack created successfully",
    });
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
}

async function getOne(req, res, next) {
  try {
    const code = req.params.code;

    // Main services pack query
    const servicesPack = await ServicesPackModel.findOne({
      where: { code },
    });

    if (!servicesPack) {
      throw new NotFoundError("Services pack not found", "ServicesPack");
    }

    // Get associated services with full details
    const associatedServices = await ServiceModel.findAll({
      include: [
        {
          model: ServicesPackModel,
          where: { code },
          attributes: [],
          through: { attributes: [] },
        },
        {
          model: OperationModel,
          through: { attributes: [] },
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
        {
          model: ProductsModel,
          through: { attributes: [] },
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
        {
          model: NapModel,
          through: { attributes: [] },
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
        {
          model: PartnerModel,
        },
        {
          model: CurrencyModel,
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
        {
          model: TaxModel,
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
      ],
      attributes: { exclude: ["createdAt", "updatedAt"] },
    });

    // For each service, get the detailed actors information
    const servicesWithActors = await Promise.all(
      associatedServices.map(async (service) => {
        const actors = await ServiceActorModel.findAll({
          where: { service_reference: service.reference },
          attributes: { exclude: ["updatedAt", "createdAt"] },
        });

        return {
          ...service.toJSON(),
          actors: actors.map((actor) => actor.toJSON()),
        };
      })
    );

    res.send({
      data: {
        ...servicesPack.toJSON(),
        services: servicesWithActors,
      }
    
    });
  } catch (err) {
    next(err);
  }
}

async function search(req, res, next) {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit) : config.limit;
    const offset = req.query.offset
      ? parseInt(req.query.offset) * limit
      : config.offset * limit;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;

    const where = req.filterConditions || {};

    Object.keys(req.query).forEach((key) => {
      if (!["offset", "limit", "sort_by", "order_by"].includes(key)) {
        where[key] = req.query[key];
      }
    });

    // Get all service packs with basic service info
    const { count, rows } = await ServicesPackModel.findAndCountAll({
      order: [[sortBy, orderBy]],
      offset,
      limit,
      where,
      distinct: true,
      include: [
        {
          model: ServiceModel,
          through: { attributes: [] },
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
      ],
    });

    // Enhance each service pack with detailed service information
    const detailedServicePacks = await Promise.all(
      rows.map(async (pack) => {
        const packJson = pack.toJSON();

        // Get detailed services for this pack
        const detailedServices = await ServiceModel.findAll({
          include: [
            {
              model: ServicesPackModel,
              where: { code: pack.code },
              attributes: [],
              through: { attributes: [] },
            },
            {
              model: OperationModel,
              through: { attributes: [] },
              attributes: { exclude: ["createdAt", "updatedAt"] },
            },
            {
              model: ProductsModel,
              through: { attributes: [] },
              attributes: { exclude: ["createdAt", "updatedAt"] },
            },
            {
              model: NapModel,
              through: { attributes: [] },
              attributes: { exclude: ["createdAt", "updatedAt"] },
            },
            { model: PartnerModel },
            {
              model: CurrencyModel,
              attributes: { exclude: ["createdAt", "updatedAt"] },
            },
            {
              model: TaxModel,
              attributes: { exclude: ["createdAt", "updatedAt"] },
            },
          ],
          attributes: { exclude: ["createdAt", "updatedAt"] },
        });

        // For each service, get actors
        const servicesWithActors = await Promise.all(
          detailedServices.map(async (service) => {
            const actors = await ServiceActorModel.findAll({
              where: { service_reference: service.reference },
              attributes: { exclude: ["updatedAt", "createdAt"] },
            });

            return {
              ...service.toJSON(),
              actors: actors.map((actor) => actor.toJSON()),
            };
          })
        );

        return {
          ...packJson,
          services: servicesWithActors,
        };
      })
    );

    res.send({
      data: detailedServicePacks,
      total_count: count,
    });
  } catch (error) {
    next(error);
  }
}

async function updateServicesPack(req, res, next) {
  const code = req.params.code;
  const payload = req.body;
  console.debug("Received payload for services pack update:", payload);

  const transaction = await sequelize.transaction();
  console.debug("Transaction started for services pack update");

  try {
    // Check if services pack exists
    const existingServicesPack = await ServicesPackModel.findOne({
      where: { code },
      transaction,
    });

    if (!existingServicesPack) {
      throw new NotFoundError("Services pack not found", "ServicesPack");
    }

    // Update services pack data
    const servicesPackData = {
      label: payload.label,
      invoice_grouping_code: payload.invoice_grouping_code,
      grouping_code_regulation: payload.grouping_code_regulation,
      type: payload.type,
    };

    await ServicesPackModel.update(servicesPackData, {
      where: { code },
      transaction,
    });

    // Update service associations if provided
    if (payload.services) {
      // Get existing service associations
      const existingAssociations = await ServiceServicesPackModel.findAll({
        where: { service_pack_code: code },
        attributes: ["service_reference"],
        raw: true,
        transaction,
      });

      const existingServiceReferences = existingAssociations.map(
        (assoc) => assoc.service_reference
      );
      const newServiceReferences = payload.services;

      // Find services to remove (exist in DB but not in payload)
      const servicesToRemove = existingServiceReferences.filter(
        (ref) => !newServiceReferences.includes(ref)
      );

      // Find services to add (exist in payload but not in DB)
      const servicesToAdd = newServiceReferences.filter(
        (ref) => !existingServiceReferences.includes(ref)
      );

      // Remove services that are no longer needed
      if (servicesToRemove.length > 0) {
        await ServiceServicesPackModel.destroy({
          where: {
            service_pack_code: code,
            service_reference: { [Op.in]: servicesToRemove },
          },
          transaction,
        });
      }

      // Add new services
      if (servicesToAdd.length > 0) {
        await Promise.all(
          servicesToAdd.map(async (serviceReference) => {
            try {
              // Check if service exists
              const service = await ServiceModel.findOne({
                where: { reference: serviceReference },
                transaction,
              });

              if (!service) {
                console.warn(
                  `Service with reference ${serviceReference} not found.`
                );
                return;
              }

              // Create association
              await ServiceServicesPackModel.findOrCreate({
                where: {
                  service_pack_code: code,
                  service_reference: serviceReference,
                },
                defaults: {
                  reference: uuidv4(),
                },
                transaction,
              });
            } catch (error) {
              console.error(
                `Error associating service ${serviceReference}:`,
                error.message
              );
            }
          })
        );
      }
    }

    // Send sync message for event streaming
    await sendSyncMessage({
      entityName: "ServicesPack",
      entityReference: code,
      operationType: OperationType.PUT,
    });

    // Commit the transaction
    await transaction.commit();

    // Retrieve the updated services pack
    const updatedServicesPack = await ServicesPackModel.findOne({
      where: { code },
    });

    res.status(200).send({
      data: updatedServicesPack,
      message: "Services pack updated successfully",
    });
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
}

async function remove(req, res, next) {
  try {
    const code = req.params.code;

    const servicesPack = await ServicesPackModel.findOne({
      where: { code },
    });

    if (!servicesPack) {
      throw new NotFoundError("Services pack not found", "ServicesPack");
    }

    // Send sync message before deletion
    await sendSyncMessage({
      entityName: "ServicesPack",
      entityReference: code,
      operationType: OperationType.DELETE,
    });

    await ServicesPackModel.destroy({
      where: { code },
    });

    res.send({
      data: {
        message: `Services pack with code ${code} has been deleted successfully`,
      },
    });
  } catch (error) {
    next(error);
  }
}

async function removeServiceFromPack(req, res, next) {
  const { code, serviceReference } = req.params;
  console.debug(
    `Removing service ${serviceReference} from services pack ${code}`
  );

  const transaction = await sequelize.transaction();

  try {
    // Check if services pack exists
    const servicesPack = await ServicesPackModel.findOne({
      where: { code: code },
      transaction,
    });

    if (!servicesPack) {
      throw new NotFoundError("Services pack not found", "ServicesPack");
    }

    // Check if the service association exists
    const association = await ServiceServicesPackModel.findOne({
      where: {
        service_pack_code: code,
        service_reference: serviceReference,
      },
      transaction,
    });

    if (!association) {
      throw new NotFoundError(
        "Service is not associated with this services pack",
        "ServiceServicesPack"
      );
    }

    // Remove the specific service association
    await ServiceServicesPackModel.destroy({
      where: {
        service_pack_code: code,
        service_reference: serviceReference,
      },
      transaction,
    });

    // Send sync message for event streaming
    await sendSyncMessage({
      entityName: "ServicesPack",
      entityReference: code,
      operationType: OperationType.PUT,
    });

    await transaction.commit();

    res.status(200).send({
      message: `Service ${serviceReference} has been removed from services pack ${code} successfully`,
    });
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
}

module.exports = {
  createServicesPack,
  getOne,
  search,
  updateServicesPack,
  remove,
  removeServiceFromPack,
};
