package com.datatricks.actors.repository;

import com.datatricks.actors.model.Address;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface AddressRepository extends JpaRepository<Address, Long>, JpaSpecificationExecutor<Address> {
    Address findByActorIdAndIdAndDeletedAtIsNull(Long actorId, Long id);

    List<Address> findByActorIdAndDeletedAtIsNullAndIsBillingIsTrue(Long actorId);

    int countByActorIdAndDeletedAtIsNull(Long actorId);

    boolean existsByIdAndDeletedAtIsNull(Long id);

    boolean existsByReferenceAndActorReferenceAndDeletedAtIsNull(String reference,String actorReference);

    Optional<Address> findByExternalReferenceAndDeletedAtIsNull(String externalReference);

    @Query("SELECT a FROM Address a WHERE a.reference = ?1 AND a.actor.reference = ?2 AND a.deletedAt IS NULL")
    Optional<Address> findByReferenceAndActorReferenceAndDeletedAtIsNull(String reference, String actorReference);
}
