package com.datatricks.actors.service;

import com.datatricks.actors.exception.MultipleResourcesNotFoundException;
import com.datatricks.actors.exception.ResourcesNotFoundException;
import com.datatricks.actors.model.*;
import com.datatricks.actors.model.dto.*;
import com.datatricks.kafkacommondomain.model.SingleResultDto;
import com.datatricks.actors.repository.*;
import com.datatricks.kafkacommondomain.model.inject.*;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ValidationException;
import jakarta.validation.Validator;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class ActorInjectService {
    private final ActorRepository actorRepository;
    private final LegalActivityRepository legalActivityRepository;
    private final PhaseRepository phaseRepository;
    private final MilestoneRepository milestoneRepository;
    private final CountryRepository countryRepository;
    private final LegalCategoryRepository legalCategoryRepository;
    private final RoleRepository roleRepository;

    private final ActorService actorService;
    private final ActorRoleService actorRoleService;
    private final AddressService addressService;
    private final BankService bankService;
    private final ContactService contactService;
    private final CommunicationMeanService communicationMeanService;
    private final Validator validator;
    private final ModelMapper modelMapper;
    private static final String ACTEUR = "ACTEUR";
    private static final String MODULE = "ACTOR-INJECT";
    private final ActorRoleRepository actorRoleRepository;

    public ActorInjectService(ModelMapper modelMapper,
                              ActorRepository actorRepository,
                              LegalActivityRepository legalActivityRepository,
                              PhaseRepository phaseRepository,
                              MilestoneRepository milestoneRepository,
                              CountryRepository countryRepository,
                              LegalCategoryRepository legalCategoryRepository,
                              RoleRepository roleRepository,
                              ActorRoleService actorRoleService,
                              ActorService actorService,
                              AddressService addressService,
                              BankService bankService,
                              ContactService contactService,
                              CommunicationMeanService communicationMeanService,
                              Validator validator, ActorRoleRepository actorRoleRepository) {
        this.modelMapper = modelMapper;
        this.actorRepository = actorRepository;
        this.legalActivityRepository = legalActivityRepository;
        this.phaseRepository = phaseRepository;
        this.milestoneRepository = milestoneRepository;
        this.countryRepository = countryRepository;
        this.legalCategoryRepository = legalCategoryRepository;
        this.roleRepository = roleRepository;
        this.actorService = actorService;
        this.actorRoleService = actorRoleService;
        this.addressService = addressService;
        this.bankService = bankService;
        this.contactService = contactService;
        this.communicationMeanService = communicationMeanService;
        this.validator = validator;
        this.actorRoleRepository = actorRoleRepository;
    }

    @Transactional
    public SingleResultDto<ActorInjectDto> injectActor(ActorInjectDto actor) {

        if (actor == null) {
            throw new ResourcesNotFoundException("Actor content must not be null", "ACTOR", MODULE);
        }
        Set<String> notFoundEntities = new HashSet<>();
        validateStaticTable(actor, notFoundEntities);

        NewActorDto newActorDto = modelMapper.map(actor, NewActorDto.class);
        String principalRoleCode = null;
        List<RoleInjectDto> remainingRoles = new ArrayList<>();
        try {
            List<RoleInjectDto> principalRole = actor.getRoles().stream()
                    .filter(RoleInjectDto::getIsPrincipal)
                    .toList();
            remainingRoles = actor.getRoles().stream()
                    .filter(role -> !role.getIsPrincipal())
                    .toList();
            principalRoleCode = principalRole.getFirst().getCode();
        } catch (Exception e) {
            notFoundEntities.add("Principal role not found");
        }
        newActorDto.setRoleCode(principalRoleCode);

        // validations
        validation(actor, newActorDto, remainingRoles, notFoundEntities);

        // call services
        injectActor(actor, newActorDto, principalRoleCode);
        injectRole(actor, principalRoleCode, remainingRoles);
        injectAddress(actor);
        injectBankAccount(actor);
        injectContact(actor);

        return SingleResultDto.<ActorInjectDto>builder()
                .data(actor)
                .build();
    }

    private void injectActor(ActorInjectDto actorInjectDto, NewActorDto newActorDto, String principalRoleCode) {
        ActorDto actor = null;
        Actor existingActor = actorRepository.findByNationalIdentityAndDeletedAtIsNull(actorInjectDto.getNationalIdentity())
                .orElse(null);
        if (existingActor != null) {
            actor = modelMapper.map(existingActor, ActorDto.class);
        } else {
            newActorDto.setRoleCode(principalRoleCode);
            var createdActor = actorService.createActor(newActorDto);
            if (createdActor != null && createdActor.getBody() != null && createdActor.getBody().getData() != null) {
                actor = createdActor.getBody().getData();
            } else {
                throw new ResourcesNotFoundException("Actor not found", "ACTOR", MODULE);
            }
        }
        actorInjectDto.setId(actor.getId());
        actorInjectDto.setReference(actor.getReference());
    }

    private void injectRole(ActorInjectDto actor, String principalRoleCode, List<RoleInjectDto> remainingRoles) {
        actor.getRoles().stream()
                .filter(role -> role.getCode().equals(principalRoleCode))
                .findFirst()
                .ifPresent(role -> role.setId(actor.getId()));

        if (!remainingRoles.isEmpty()) {
            for (RoleInjectDto role : remainingRoles) {

                ActorRole actorRole = actorRoleRepository
                        .findByActorIdIdAndRoleCodeAndDeletedAtIsNull(actor.getId(), role.getCode())
                        .orElse(null);
                if (actorRole != null) {
                    break;
                }
                NewRoleDto roleDto = modelMapper.map(role, NewRoleDto.class);
                if (role.getBusinessReference() != null) {
                    roleDto.setBusinessReference(role.getBusinessReference().getReference());
                }
                ActorRoleDto createdActorRole = actorRoleService.saveNewActorRole(actor.getId(), roleDto).getBody().getData();
                actor.getRoles().stream()
                        .filter(r -> r.getCode().equals(createdActorRole.getRole().getCode()))
                        .findFirst()
                        .ifPresent(r -> {
                            r.setId(createdActorRole.getId());
                            if(r.getBusinessReference() != null && createdActorRole.getBusinessReference() != null) {
                                r.getBusinessReference().setId(createdActorRole.getBusinessReference().getId());
                            }
                        });
            }
        }
    }

    private void injectAddress(ActorInjectDto actor) {
        if (actor.getAddresses() != null) {
            for (AddressInjectDto address : actor.getAddresses()) {
                Address existingAddress
                AddressDto addressDto = modelMapper.map(address, AddressDto.class);
                AddressDto createdAddress = addressService.createAddress(actor.getId(), addressDto).getBody().getData();
                actor.getAddresses().stream()
                        .filter(a -> a.equals(address))
                        .findFirst()
                        .ifPresent(a -> {
                            //Do not set the id of the address, we need to keep the id of the address in the contract inject
                            a.setReference(createdAddress.getReference());
                            a.setExternalReference(address.getExternalReference());
                            a.getPhysicalAddress().setId(createdAddress.getPhysicalAddress().getId());
                        });
            }
        }
    }

    private void injectBankAccount(ActorInjectDto actor) {
        if (actor.getBankAccounts() != null) {
            for (BankAccountInjectDto bank : actor.getBankAccounts()) {
                NewBankAccountDto bankDto = modelMapper.map(bank, NewBankAccountDto.class);
                BankAccountDto createdBank = bankService.createBankAccount(actor.getReference(), bankDto).getBody().getData();
                actor.getBankAccounts().stream()
                        .filter(b -> b.equals(bank))
                        .findFirst()
                        .ifPresent(b -> b.setId(createdBank.getId()));
            }
        }
    }

    private void injectContact(ActorInjectDto actor) {
        if (actor.getContacts() != null) {
            for (ContactInjectDto contact : actor.getContacts()) {
                ContactDto contactDto = modelMapper.map(contact, ContactDto.class);
                ContactDto createdContact = contactService.createNewContact(
                        actor.getId(), contactDto).getBody().getData();
                actor.getContacts().stream()
                        .filter(c -> c.equals(contact))
                        .findFirst()
                        .ifPresent(c -> c.setId(createdContact.getId()));

                if (contact.getCommunicationMeans() == null) {
                    break;
                }
                for (CommunicationMeanInjectDto communicationMean : contact.getCommunicationMeans()) {
                    CommunicationMeanDto communicationMeanDto = modelMapper.map(communicationMean, CommunicationMeanDto.class);
                    CommunicationMeanDto createdComm = communicationMeanService.createNewCommunicationMean(createdContact.getId(), communicationMeanDto)
                            .getBody()
                            .getData();
                    actor.getContacts().stream()
                            .filter(c -> c.equals(contact))
                            .findFirst()
                            .ifPresent(c -> c.getCommunicationMeans().stream()
                                    .filter(cm -> cm.equals(communicationMean))
                                    .findFirst()
                                    .ifPresent(cm -> cm.setId(createdComm.getId())));

                }
            }
        }
    }

    private void validation(ActorInjectDto actor, NewActorDto newActorDto, List<RoleInjectDto> remainingRoles, Set<String> notFoundEntities) {
        Set<Set<ConstraintViolation<Object>>> violations = new HashSet<>();
        if (!validator.validate(newActorDto).isEmpty()) {
            violations.add(validator.validate(newActorDto));
        }

        if (!remainingRoles.isEmpty()) {
            for (RoleInjectDto role : remainingRoles) {
                NewRoleDto roleDto = modelMapper.map(role, NewRoleDto.class);
                if (!validator.validate(roleDto).isEmpty()) {
                    violations.add(validator.validate(roleDto));
                }
            }
        }

        if (actor.getAddresses() != null) {
            for (AddressInjectDto address : actor.getAddresses()) {
                AddressDto addressDto = modelMapper.map(address, AddressDto.class);
                if (!validator.validate(addressDto).isEmpty()) {
                    violations.add(validator.validate(addressDto));
                }
            }
        }

        if (actor.getBankAccounts() != null) {
            for (BankAccountInjectDto bank : actor.getBankAccounts()) {
                NewBankAccountDto bankDto = modelMapper.map(bank, NewBankAccountDto.class);
                if (!validator.validate(bankDto).isEmpty()) {
                    violations.add(validator.validate(bankDto));
                }
            }
        }

        if (actor.getContacts() != null) {
            for (ContactInjectDto contact : actor.getContacts()) {
                ContactDto contactDto = modelMapper.map(contact, ContactDto.class);
                if (!validator.validate(contactDto).isEmpty()) {
                    violations.add(validator.validate(contactDto));
                }

                if (contact.getCommunicationMeans() == null) {
                    break;
                }
                for (CommunicationMeanInjectDto communicationMean : contact.getCommunicationMeans()) {
                    CommunicationMeanDto communicationMeanDto = modelMapper.map(communicationMean, CommunicationMeanDto.class);
                    if (!validator.validate(communicationMeanDto).isEmpty()) {
                        violations.add(validator.validate(communicationMeanDto));
                    }
                }
            }
        }

        if (!violations.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (Set<ConstraintViolation<Object>> violationSet : violations) {
                for (ConstraintViolation<Object> violation : violationSet) {
                    sb.append(violation.getMessage()).append("\n");
                }
            }
            // You can throw an exception or handle the validation errors as needed
            throw new ValidationException(sb.toString());
        }
        if (!notFoundEntities.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (String entity : notFoundEntities) {
                sb.append(entity).append("\n");
            }
            // You can throw an exception or handle the validation errors as needed
            throw new MultipleResourcesNotFoundException(sb.toString(), "ACTOR", MODULE);
        }
    }

    private void validateStaticTable(ActorInjectDto actor, Set<String> notFoundEntities) {
        if (actor.getActivity() != null) {
            Optional<LegalActivities> activity = legalActivityRepository.findByCode(actor.getActivity().getCode()).or(
                    () -> {
                        notFoundEntities.add("LegalActivities not found for code: " + actor.getActivity().getCode());
                        return Optional.empty();
                    });
            activity.ifPresent(a -> actor.getActivity().setId(a.getId()));
        } else {
            notFoundEntities.add("LegalActivities must not be null");
        }

        if (actor.getPhase() != null) {
            Optional<Phase> phase = phaseRepository.findByCodeAndAssociatedTo(actor.getPhase().getCode(), ACTEUR).or(
                    () -> {
                        notFoundEntities.add("Phase not found for code: " + actor.getPhase().getCode());
                        return Optional.empty();
                    });
            phase.ifPresent(p -> actor.getPhase().setId(p.getId()));
        } else {
            notFoundEntities.add("Phase must not be null");
        }

        if (actor.getMilestone() != null) {
            Optional<Milestone> milestone = milestoneRepository.findByCode(actor.getMilestone().getCode()).or(
                    () -> {
                        notFoundEntities.add("Milestone not found for code: " + actor.getMilestone().getCode());
                        return Optional.empty();
                    });
            milestone.ifPresent(m -> actor.getMilestone().setId(m.getId()));
        } else {
            notFoundEntities.add("Milestones must not be null");
        }

        if (actor.getCountry() != null) {
            Optional<Country> country = countryRepository.findByCode(actor.getCountry().getCode()).or(
                    () -> {
                        notFoundEntities.add("Country not found for code: " + actor.getCountry().getCode());
                        return Optional.empty();
                    });
            country.ifPresent(c -> actor.getCountry().setId(c.getId()));
        } else {
            notFoundEntities.add("Country must not be null");
        }

        if (actor.getLegalCategory() != null) {
            Optional<LegalCategory> legalCategory = legalCategoryRepository.findByCode(actor.getLegalCategory().getCode()).or(
                    () -> {
                        notFoundEntities.add("Legal_category not found for code: " + actor.getLegalCategory().getCode());
                        return Optional.empty();
                    });
            legalCategory.ifPresent(lc -> actor.getLegalCategory().setId(lc.getId()));
        } else {
            notFoundEntities.add("Legal_category must not be null");
        }

        if (actor.getRoles() != null) {
            actor.getRoles().forEach(
                    role -> roleRepository.findByCode(role.getCode()).or(
                            () -> {
                                notFoundEntities.add("Role not found for code: " + role.getCode());
                                return Optional.empty();
                            }));
        } else {
            notFoundEntities.add("Roles must not be null");
        }
    }

    @Transactional
    public void deleteAll(List<String> references) {
        actorRepository.deleteAllByReferenceIn(references);
    }
}
