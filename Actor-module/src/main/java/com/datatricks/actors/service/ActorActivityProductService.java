package com.datatricks.actors.service;

import com.datatricks.actors.exception.ConflictException;
import com.datatricks.actors.exception.ResourcesNotFoundException;
import com.datatricks.actors.exception.handler.InformativeMessage;
import com.datatricks.actors.model.Activity;
import com.datatricks.actors.model.ActorActivityProduct;
import com.datatricks.actors.model.OperationNatureTypeMapping;
import com.datatricks.actors.model.ProductLineTypeMapping;
import com.datatricks.actors.model.dto.ActorActivityProductDTO;
import com.datatricks.actors.model.dto.PageDto;
import com.datatricks.actors.model.dto.PatchActorActivityProductDTO;
import com.datatricks.actors.model.dto.ProductStatusDTO;
import com.datatricks.actors.repository.*;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ActorActivityProductService {
    private final ActorActivityProductRepository actorActivityProductRepository;
    private final ActivityRepository activityRepository;
    private final ProductRepository productRepository;
    private final ActorRepository actorRepository;
    private final ProductLineTypeMappingRepository productLineTypeMappingRepository;

    @Transactional
    public ResponseEntity<PageDto<ActorActivityProductDTO>> getActorActivityProducts(String actorReference) {
        // Fetch all activities for the actor in a single query
        List<Activity> activities = actorActivityProductRepository.findActivitiesByActorReference(actorReference);

        if (activities.isEmpty()) {
            return ResponseEntity.ok().body(PageDto.<ActorActivityProductDTO>builder()
                    .data(List.of())
                    .total(0)
                    .build());
        }

        // Fetch all product codes for preloading
        List<String> activityCodes = activities.stream()
                .map(Activity::getCode)
                .collect(Collectors.toList());

        // Get all actor-activity-products in one query
        List<ActorActivityProduct> allActorActivityProducts = actorActivityProductRepository
                .findByActorReferenceAndActivityCodeIn(actorReference, activityCodes);

        // Group by activity code for faster lookup
        Map<String, List<ActorActivityProduct>> productsByActivityCode = allActorActivityProducts.stream()
                .collect(Collectors.groupingBy(aap -> aap.getActivity().getCode()));

        // Collect all product codes for bulk fetching
        Set<String> allProductCodes = allActorActivityProducts.stream()
                .map(aap -> aap.getProduct().getCode())
                .collect(Collectors.toSet());

        // Fetch all product line type mappings in one query
        Map<String, List<ProductLineTypeMapping>> mappingsByProductCode =
                productLineTypeMappingRepository.findByProduct_CodeIn(new ArrayList<>(allProductCodes)).stream()
                        .collect(Collectors.groupingBy(ProductLineTypeMapping::getProductCode));

        // Build the response DTOs
        List<ActorActivityProductDTO> actorActivityProductDTOs = activities.stream()
                .map(activity -> {
                    String activityCode = activity.getCode();
                    List<ActorActivityProduct> activityProducts = productsByActivityCode.getOrDefault(activityCode, List.of());

                    if (activityProducts.isEmpty()) {
                        return null;
                    }

                    List<ProductStatusDTO> products = activityProducts.stream()
                            .map(aap -> {
                                String productCode = aap.getProduct().getCode();
                                List<ProductLineTypeMapping> productMappings =
                                        mappingsByProductCode.getOrDefault(productCode, List.of());

                                List<String> mappingLabels = productMappings.stream()
                                        .map(pltm -> {
                                            return pltm.getOperationNatureTypeMapping() != null ?
                                                    pltm.getOperationNatureTypeMapping().getOperationNatureMapping().getOperation().getLabel()
                                                    + " / " + pltm.getOperationNatureTypeMapping().getOperationNatureMapping().getNature().getLabel()
                                                    + " / " + pltm.getOperationNatureTypeMapping().getType().getLabel()
                                                    : "";
                                        })
                                        .filter(label -> !label.isEmpty())
                                        .collect(Collectors.toList());

                                return new ProductStatusDTO(
                                        aap.getProduct().getId(),
                                        productCode,
                                        aap.getProduct().getLabel(),
                                        aap.isActive(),
                                        mappingLabels
                                );
                            })
                            .collect(Collectors.toList());

                    return new ActorActivityProductDTO(
                            activityProducts.isEmpty() ? null : activityProducts.getFirst().getId(),
                            activityCode,
                            activity.getLabel(),
                            products
                    );
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return ResponseEntity.ok().body(PageDto.<ActorActivityProductDTO>builder()
                .data(actorActivityProductDTOs)
                .total(actorActivityProductDTOs.size())
                .build());
    }

    @Transactional
    public ResponseEntity<PageDto<ActorActivityProductDTO>> saveActorActivityProducts(String actorReference,
                                                                                      ActorActivityProductDTO actorActivityProductDTO) {
        List<ActorActivityProduct> actorActivityProducts =
                actorActivityProductRepository.findByActorReferenceAndActivityCode(actorReference, actorActivityProductDTO.getActivityCode());
        if (actorActivityProducts == null) {
            actorActivityProducts = new ArrayList<>();
        }
        List<ActorActivityProduct> newActorActivityProducts = new ArrayList<>();
        var products = productRepository.findAllByActivityCode(actorActivityProductDTO.getActivityCode());
        for (var productStatusDTO : products) {
            var actorActivityProductCount = actorActivityProducts.stream()
                    .filter(aap -> aap.getProduct().getCode().equals(productStatusDTO.getCode()))
                    .count();
            if (actorActivityProductCount == 0) {
                ActorActivityProduct actorActivityProduct = new ActorActivityProduct();
                actorActivityProduct.setActor(actorRepository.findByReferenceAndDeletedAtIsNull(actorReference)
                        .orElseThrow(() -> new ResourcesNotFoundException("Actor", "reference", actorReference)));
                actorActivityProduct.setActivity(activityRepository.findByCodeAndDeletedAtIsNull(((actorActivityProductDTO.getActivityCode())))
                        .orElseThrow(() -> new ResourcesNotFoundException("Activity", "code", actorActivityProductDTO.getActivityCode())));
                actorActivityProduct.setProduct(productStatusDTO);
                actorActivityProduct.setActive(true);
                newActorActivityProducts.add(actorActivityProduct);
            } else {
                throw new ConflictException("Actor, Activity and Product already exists",
                        "ACTOR_ACTIVITY_ALREADY_EXISTS",
                        "The relation Actor, Activity and Product already exists",
                        ActorActivityProductService.class.getName());
            }
        }
        actorActivityProductRepository.saveAll(newActorActivityProducts);
        return getActorActivityProducts(actorReference);
    }

    @Transactional
    public ResponseEntity<PageDto<ActorActivityProductDTO>> patchActorActivityProducts(String actorReference,
                                                                                       PatchActorActivityProductDTO patchActorActivityProductDTO) {
        ActorActivityProduct actorActivityProducts =
                actorActivityProductRepository.findByActorReferenceAndActivityCodeAndProductCode(actorReference, patchActorActivityProductDTO.getActivityCode(), patchActorActivityProductDTO.getProductCode());
        if (actorActivityProducts == null) {
            throw new ResourcesNotFoundException("ActorActivityProduct", "actorReference, activityCode, productCode",
                    actorReference + ", " + patchActorActivityProductDTO.getActivityCode() + ", " + patchActorActivityProductDTO.getProductCode());
        }
        actorActivityProducts.setActive(patchActorActivityProductDTO.isActive());
        return getActorActivityProducts(actorReference);
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteActorActivityProducts(String actorReference,
                                                                          String activityCode) {
        List<ActorActivityProduct> actorActivityProducts =
                actorActivityProductRepository.findByActorReferenceAndActivityCode(actorReference, activityCode);
        if (actorActivityProducts == null) {
            throw new ResourcesNotFoundException("ActorActivityProduct", "actorReference, activityCode",
                    actorReference + ", " + activityCode);
        }
        actorActivityProductRepository.deleteAll(actorActivityProducts);
        return ResponseEntity.ok().body(new InformativeMessage("ActorActivityProduct deleted successfully"));
    }
}
