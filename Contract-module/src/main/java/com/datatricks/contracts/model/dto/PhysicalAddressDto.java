package com.datatricks.contracts.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PhysicalAddressDto {
    @JsonProperty("id")
    @Schema(description = "id of the physical address", example = "1")
    @JsonIgnore
    private Long id;

    @JsonProperty("status")
    @Schema(description = "status of the physical address", example = "true")
    private boolean status;

    @JsonProperty("department")
    @Schema(description = "department of the physical address", example = "IT")
    private String department;

    @JsonProperty("sub_department")
    @Schema(description = "sub department of the physical address", example = "DEV")
    private String subDepartment;

    @JsonProperty("building_name")
    @Schema(description = "building name of the physical address", example = "")
    private String buildingName;

    @JsonProperty("floor")
    @Schema(description = "floor of the physical address", example = "IT")
    private String floor;

    @JsonProperty("room")
    @Schema(description = "room of the physical address", example = "IT")
    private String room;

    @JsonProperty("street_name")
    @Schema(description = "street name of the physical address", example = "IT")
    private String streetName;

    @JsonProperty("building_number")
    @Schema(description = "building number of the physical address", example = "IT")
    private String buildingNumber;

    @JsonProperty("post_box")
    @Schema(description = "post box of the physical address", example = "IT")
    private String postBox;

    @JsonProperty("town_location_name")
    @Schema(description = "town location name of the physical address", example = "IT")
    private String townLocationName;

    @JsonProperty("post_code")
    @Schema(description = "post code of the physical address", example = "IT")
    private String postCode;

    @JsonProperty("town_name")
    @Schema(description = "town name of the physical address", example = "IT")
    private String townName;

    @JsonProperty("country")
    @Schema(description = "country of the physical address", example = "IT")
    private String country;

    @JsonProperty("district_name")
    @Schema(description = "district name of the physical address", example = "IT")
    private String districtName;

    @JsonProperty("country_sub_division")
    @Schema(description = "country subdivision of the physical address", example = "IT")
    private String countrySubDivision;
}
