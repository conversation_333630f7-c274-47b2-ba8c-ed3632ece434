<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:pro="http://www.liquibase.org/xml/ns/pro"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd
      http://www.liquibase.org/xml/ns/pro
      http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd">
    <property name="table.prefix" value="DT_"/>
    <include file="db/changeLog/changes/PostgresMigration.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-countries-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-phases-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-currencies-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-roles-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-milestones-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-activities-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-Asset-Categories-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/trigger-assets-serial-number.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/trigger-elements-serial-number.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-taux.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-tax.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2373-update-phase.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/phase-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/role-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/milestones-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/activities-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/countries-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/currencies-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/legal-categories-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/categories-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2619-remove-bank-account-table.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2641.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2933.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3090.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3286.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3422.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3347.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3228.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3516.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3543.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3457.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3583.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3606.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3467-integrate-filter-sidebar-for-sub-asset-list.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3708.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3544-add-image-sub-asset.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3747.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3759.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3465-backend-implement-synchronization-between-settings-module-and-backend-side.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3788-manage-roles-add-edit-activate-deactivate-delete.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/fix-bug-tax-rate.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3815-refactor-operations-nature-types-by-code.xml"
             relativeToChangelogFile="false"/>
   <include file="/db/changeLog/changes/Sync-nap-table.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3738.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3870.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-bug-tax-tax-rate.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-1007-injection.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
