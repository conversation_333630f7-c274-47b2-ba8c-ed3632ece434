package com.datatricks.assetmodule.repository;

import com.datatricks.assetmodule.model.Asset;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface AssetRepository extends JpaRepository<Asset, Long>, JpaSpecificationExecutor<Asset> {
    Optional<Asset> findByIdAndDeletedAtIsNull(Long assetId);

    boolean existsByIdAndDeletedAtIsNull(Long assetId);

    // update the asset TotalValue
    @Modifying
    @Query("update Asset a set a.totalValue = :totalValue where a.id = :assetId")
    void updateTotalValue(Long assetId, Double totalValue);

    Optional<Asset> findByReferenceAndDeletedAtIsNull(String assetReference);
    Optional<Asset> findByExternalReferenceAndDeletedAtIsNull (String assetReference);

}
